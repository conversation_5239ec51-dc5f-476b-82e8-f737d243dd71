/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn special-lint-fix` to update
 */
"use strict";function e(r,{instancePath:t="",parentData:o,parentDataProperty:n,rootData:a=r}={}){let s=null,l=0;if(0===l){if(!r||"object"!=typeof r||Array.isArray(r))return e.errors=[{params:{type:"object"}}],!1;{const t=l;for(const t in r)if("esModule"!==t&&"exportsConvention"!==t&&"exportsOnly"!==t&&"localIdentName"!==t)return e.errors=[{params:{additionalProperty:t}}],!1;if(t===l){if(void 0!==r.esModule){const t=l;if("boolean"!=typeof r.esModule)return e.errors=[{params:{type:"boolean"}}],!1;var i=t===l}else i=!0;if(i){if(void 0!==r.exportsConvention){let t=r.exportsConvention;const o=l,n=l;let a=!1;const c=l;if("as-is"!==t&&"camel-case"!==t&&"camel-case-only"!==t&&"dashes"!==t&&"dashes-only"!==t){const e={params:{}};null===s?s=[e]:s.push(e),l++}var p=c===l;if(a=a||p,!a){const e=l;if(!(t instanceof Function)){const e={params:{}};null===s?s=[e]:s.push(e),l++}p=e===l,a=a||p}if(!a){const r={params:{}};return null===s?s=[r]:s.push(r),l++,e.errors=s,!1}l=n,null!==s&&(n?s.length=n:s=null),i=o===l}else i=!0;if(i){if(void 0!==r.exportsOnly){const t=l;if("boolean"!=typeof r.exportsOnly)return e.errors=[{params:{type:"boolean"}}],!1;i=t===l}else i=!0;if(i)if(void 0!==r.localIdentName){const t=l;if("string"!=typeof r.localIdentName)return e.errors=[{params:{type:"string"}}],!1;i=t===l}else i=!0}}}}}return e.errors=s,0===l}function r(t,{instancePath:o="",parentData:n,parentDataProperty:a,rootData:s=t}={}){let l=null,i=0;return e(t,{instancePath:o,parentData:n,parentDataProperty:a,rootData:s})||(l=null===l?e.errors:l.concat(e.errors),i=l.length),r.errors=l,0===i}module.exports=r,module.exports.default=r;