/* Dashboard Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.status-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.status-card:hover {
    transform: translateY(-2px);
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    border: none;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #6610f2);
    border: none;
}

.post-item {
    border-left: 4px solid #007bff;
    background-color: #fff;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.post-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.post-item.expanded {
    border-left-color: #28a745;
}

.post-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.post-header:hover {
    background-color: #f8f9fa;
}

.post-content {
    padding: 0 1rem 1rem 1rem;
}

.expand-icon {
    transition: transform 0.3s ease;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.comments-section {
    display: none;
    animation: slideDown 0.3s ease-out;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    margin: 0 -1rem -1rem -1rem;
    border-radius: 0 0 8px 8px;
}

.comments-section.show {
    display: block;
}

.comments-header {
    padding: 1rem;
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.comments-container {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.comment-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    border-left: 3px solid #6c757d;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.comment-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-item.spam {
    border-left-color: #dc3545;
    background-color: #fff5f5;
}

.comment-item.normal {
    border-left-color: #28a745;
    background-color: #f8fff8;
}

.prediction-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.confidence-bar {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.confidence-fill.high {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.confidence-fill.medium {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.confidence-fill.low {
    background: linear-gradient(90deg, #6c757d, #adb5bd);
}

.test-result {
    border-radius: 8px;
    padding: 1rem;
}

.test-result.spam {
    background-color: #fff5f5;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.test-result.normal {
    background-color: #f8fff8;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
        max-height: 200px;
    }
    to {
        opacity: 0;
        transform: translateX(-20px);
        max-height: 0;
        padding: 0;
        margin: 0;
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Status indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.running {
    background-color: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.stopped {
    background-color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .status-card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        font-size: 0.9rem;
    }

    .post-header {
        padding: 0.75rem;
    }

    .post-content {
        padding: 0 0.75rem 0.75rem 0.75rem;
    }

    .comments-container {
        max-height: 300px;
    }

    .comment-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .confidence-bar {
        height: 3px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #fff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #fff;
    }
    
    .card-header {
        background-color: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .comment-item {
        background-color: #404040;
    }
    
    .comment-item.spam {
        background-color: #4a2c2c;
    }
    
    .comment-item.normal {
        background-color: #2c4a2c;
    }
}
