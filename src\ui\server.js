const express = require('express');
const path = require('path');
const SpamDetectorBridge = require('../bridges/spamDetectorBridge');
const AutoSpamMonitor = require('../monitors/auto_monitor');
const FB = require('fb');
require('dotenv').config();

class UIServer {
  constructor(options = {}) {
    this.app = express();
    this.port = options.port || 3001;
    this.spamDetector = null;
    this.autoMonitor = null;
    this.commentsCache = new Map(); // Simple cache for comments
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
    this.monitorStatus = {
      isRunning: false,
      startTime: null,
      commentsProcessed: 0,
      spamRemoved: 0,
      lastCheck: null
    };

    this.setupMiddleware();
    this.setupRoutes();
    this.initializeServices();
  }

  setupMiddleware() {
    // Serve static files
    this.app.use(express.static(path.join(__dirname, 'assets')));
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Set view engine
    this.app.set('view engine', 'ejs');
    this.app.set('views', path.join(__dirname, 'pages'));
  }

  initializeServices() {
    // Initialize spam detector
    this.spamDetector = new SpamDetectorBridge({
      mode: 'child_process',
      useOptimized: true,
      timeout: 30000
    });

    // Setup Facebook API
    if (process.env.PAGE_ACCESS_TOKEN) {
      FB.setAccessToken(process.env.PAGE_ACCESS_TOKEN);
    }
  }

  setupRoutes() {
    // Main dashboard
    this.app.get('/', (req, res) => {
      res.render('dashboard', {
        title: 'Judol Remover Dashboard',
        monitorStatus: this.monitorStatus,
        pageId: process.env.PAGE_ID,
        hasToken: !!process.env.PAGE_ACCESS_TOKEN
      });
    });

    // API Routes
    this.app.get('/api/status', (req, res) => {
      res.json({
        monitor: this.monitorStatus,
        detector: {
          ready: this.spamDetector ? true : false,
          mode: this.spamDetector?.mode || 'unknown'
        },
        facebook: {
          pageId: process.env.PAGE_ID,
          hasToken: !!process.env.PAGE_ACCESS_TOKEN
        }
      });
    });

    // Test spam detection
    this.app.post('/api/test-detection', async (req, res) => {
      try {
        const { text } = req.body;
        if (!text) {
          return res.status(400).json({ error: 'Text is required' });
        }

        const result = await this.spamDetector.predict(text);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Start/Stop auto monitor
    this.app.post('/api/monitor/start', async (req, res) => {
      try {
        if (this.monitorStatus.isRunning) {
          return res.status(400).json({ error: 'Monitor already running' });
        }

        this.autoMonitor = new AutoSpamMonitor({
          pollInterval: 30000,
          debugMode: false
        });

        // Override some methods to track statistics
        const originalProcessComment = this.autoMonitor.processComment.bind(this.autoMonitor);
        this.autoMonitor.processComment = async (comment, postId) => {
          this.monitorStatus.commentsProcessed++;
          const result = await originalProcessComment(comment, postId);
          if (result) {
            this.monitorStatus.spamRemoved++;
          }
          this.monitorStatus.lastCheck = new Date();
          return result;
        };

        await this.autoMonitor.start();
        
        this.monitorStatus.isRunning = true;
        this.monitorStatus.startTime = new Date();
        this.monitorStatus.commentsProcessed = 0;
        this.monitorStatus.spamRemoved = 0;

        res.json({ success: true, message: 'Auto monitor started' });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.post('/api/monitor/stop', async (req, res) => {
      try {
        if (!this.monitorStatus.isRunning) {
          return res.status(400).json({ error: 'Monitor not running' });
        }

        if (this.autoMonitor) {
          await this.autoMonitor.stop();
          this.autoMonitor = null;
        }

        this.monitorStatus.isRunning = false;
        this.monitorStatus.startTime = null;

        res.json({ success: true, message: 'Auto monitor stopped' });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get recent posts
    this.app.get('/api/posts', async (req, res) => {
      try {
        if (!process.env.PAGE_ACCESS_TOKEN) {
          return res.status(400).json({ error: 'No access token configured' });
        }

        const posts = await new Promise((resolve, reject) => {
          FB.api(`${process.env.PAGE_ID}/posts`, {
            fields: ['id', 'message', 'created_time'],
            limit: 5
          }, (response) => {
            if (response && !response.error) {
              resolve(response.data || []);
            } else {
              reject(new Error(response.error?.message || 'Failed to get posts'));
            }
          });
        });

        res.json(posts);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get comments for a post
    this.app.get('/api/posts/:postId/comments', async (req, res) => {
      try {
        const { postId } = req.params;
        const forceRefresh = req.query.refresh === 'true';

        console.log(`📥 Loading comments for post ${postId}${forceRefresh ? ' (force refresh)' : ''}...`);
        const startTime = Date.now();

        // Check cache first (unless force refresh)
        if (!forceRefresh && this.commentsCache.has(postId)) {
          const cached = this.commentsCache.get(postId);
          if (Date.now() - cached.timestamp < this.cacheTimeout) {
            console.log(`💾 Serving from cache (${cached.data.length} comments)`);
            res.set('X-Cache-Status', 'HIT');
            res.set('X-Cache-Age', Math.floor((Date.now() - cached.timestamp) / 1000));
            return res.json(cached.data);
          } else {
            this.commentsCache.delete(postId); // Remove expired cache
          }
        }

        // Get comments from Facebook
        const comments = await new Promise((resolve, reject) => {
          FB.api(`${postId}/comments`, {
            fields: ['id', 'message', 'created_time', 'from'],
            limit: 20
          }, (response) => {
            if (response && !response.error) {
              resolve(response.data || []);
            } else {
              reject(new Error(response.error?.message || 'Failed to get comments'));
            }
          });
        });

        const fbTime = Date.now() - startTime;
        console.log(`📘 Facebook API took ${fbTime}ms for ${comments.length} comments`);

        if (comments.length === 0) {
          console.log(`📭 No comments found for post ${postId}`);
          const emptyResult = [];
          // Cache empty result for shorter time
          this.commentsCache.set(postId, {
            data: emptyResult,
            timestamp: Date.now()
          });
          return res.json(emptyResult);
        }

        // Process predictions in parallel with limited concurrency
        const predictionStartTime = Date.now();
        const commentsWithPrediction = await this.processPredictionsInBatches(comments, 5); // Increased batch size
        const predictionTime = Date.now() - predictionStartTime;

        const totalTime = Date.now() - startTime;
        console.log(`🤖 AI predictions took ${predictionTime}ms`);
        console.log(`⚡ Total request time: ${totalTime}ms`);

        // Cache the result
        this.commentsCache.set(postId, {
          data: commentsWithPrediction,
          timestamp: Date.now()
        });

        // Add performance headers
        res.set('X-Cache-Status', 'MISS');
        res.set('X-FB-Time', fbTime);
        res.set('X-AI-Time', predictionTime);
        res.set('X-Total-Time', totalTime);

        res.json(commentsWithPrediction);
      } catch (error) {
        console.error(`❌ Error loading comments:`, error.message);
        res.status(500).json({ error: error.message });
      }
    });

    // Manual delete comment
    this.app.delete('/api/comments/:commentId', async (req, res) => {
      try {
        const { commentId } = req.params;
        
        const result = await new Promise((resolve) => {
          FB.api(`/${commentId}`, 'DELETE', (response) => {
            resolve(response === true);
          });
        });

        if (result) {
          res.json({ success: true, message: 'Comment deleted' });
        } else {
          res.status(400).json({ error: 'Failed to delete comment' });
        }
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  start() {
    this.server = this.app.listen(this.port, () => {
      console.log(`🎨 UI Server running on http://localhost:${this.port}`);
      console.log(`📊 Dashboard: http://localhost:${this.port}`);
    });
  }

  async processPredictionsInBatches(comments, batchSize = 3) {
    const results = [];

    for (let i = 0; i < comments.length; i += batchSize) {
      const batch = comments.slice(i, i + batchSize);

      const batchPromises = batch.map(async (comment) => {
        try {
          const prediction = await this.spamDetector.predict(comment.message);
          return {
            ...comment,
            prediction
          };
        } catch (error) {
          console.error(`❌ Prediction error for comment ${comment.id}:`, error.message);
          return {
            ...comment,
            prediction: {
              error: error.message,
              is_spam: false,
              confidence: 0,
              label: 'error'
            }
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Small delay between batches to prevent overwhelming
      if (i + batchSize < comments.length) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }

    return results;
  }

  stop() {
    if (this.server) {
      this.server.close();
    }

    if (this.autoMonitor) {
      this.autoMonitor.stop();
    }

    if (this.spamDetector) {
      this.spamDetector.cleanup();
    }
  }
}

module.exports = UIServer;

// Jika dijalankan langsung
if (require.main === module) {
  const uiServer = new UIServer({
    port: process.env.UI_PORT || 3001
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down UI server...');
    uiServer.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down UI server...');
    uiServer.stop();
    process.exit(0);
  });

  uiServer.start();
}
