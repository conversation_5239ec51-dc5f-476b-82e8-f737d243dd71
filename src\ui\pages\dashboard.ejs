<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                Judol Remover
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-robot me-1"></i>
                    AI-Powered Spam Detection
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Status Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-play-circle fa-2x text-success mb-2"></i>
                        <h6>Monitor Status</h6>
                        <span id="monitor-status" class="badge bg-<%= monitorStatus.isRunning ? 'success' : 'secondary' %>">
                            <%= monitorStatus.isRunning ? 'Running' : 'Stopped' %>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-comments fa-2x text-info mb-2"></i>
                        <h6>Comments Processed</h6>
                        <h4 id="comments-processed"><%= monitorStatus.commentsProcessed %></h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-trash fa-2x text-danger mb-2"></i>
                        <h6>Spam Removed</h6>
                        <h4 id="spam-removed"><%= monitorStatus.spamRemoved %></h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card status-card">
                    <div class="card-body text-center">
                        <i class="fas fa-facebook fa-2x text-primary mb-2"></i>
                        <h6>Facebook Page</h6>
                        <small class="text-muted"><%= pageId || 'Not configured' %></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>Auto Monitor Control</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="start-monitor" class="btn btn-success" <%= monitorStatus.isRunning ? 'disabled' : '' %>>
                                <i class="fas fa-play me-2"></i>Start Auto Monitor
                            </button>
                            <button id="stop-monitor" class="btn btn-danger" <%= !monitorStatus.isRunning ? 'disabled' : '' %>>
                                <i class="fas fa-stop me-2"></i>Stop Auto Monitor
                            </button>
                        </div>
                        <% if (monitorStatus.isRunning && monitorStatus.startTime) { %>
                        <div class="mt-3">
                            <small class="text-muted">
                                Started: <%= new Date(monitorStatus.startTime).toLocaleString() %>
                            </small>
                        </div>
                        <% } %>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-vial me-2"></i>Test Spam Detection</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <textarea id="test-text" class="form-control" rows="3" placeholder="Masukkan teks untuk ditest..."></textarea>
                        </div>
                        <button id="test-detection" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Test Detection
                        </button>
                        <div id="test-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Posts -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-newspaper me-2"></i>Recent Posts & Comments</h5>
                        <button id="refresh-posts" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="posts-container">
                            <div class="text-center p-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-3 mb-1">Loading posts...</p>
                                <small class="text-muted">Click on posts to view comments</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle me-2"></i>
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/dashboard.js"></script>
</body>
</html>
