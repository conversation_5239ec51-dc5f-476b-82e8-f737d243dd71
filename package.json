{"name": "judol-remover", "version": "1.0.0", "main": "index.js", "scripts": {"test": "node tests/test_spam_detector.js", "start": "node src/index.js", "start-api": "node scripts/start_api_server.js", "test-detector": "node tests/test_spam_detector.js", "test-simple": "node tests/test_simple.js", "monitor": "node src/monitors/auto_monitor.js", "debug-monitor": "node tests/debug_monitor.js", "debug-facebook": "node tests/debug_facebook.js", "webhook": "node src/monitors/webhook_server.js", "install-python-deps": "pip install -r config/requirements.txt"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.8.4", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^5.1.0", "fb": "^2.0.0", "fb-sdk-wrapper": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "readline": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/node": "^7.26.0", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "babel-loader": "^10.0.0"}}