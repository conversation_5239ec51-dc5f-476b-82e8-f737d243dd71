{"name": "judol-remover", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.8.4", "dotenv": "^16.5.0", "fb": "^2.0.0", "fb-sdk-wrapper": "^1.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "readline": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/node": "^7.26.0", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "babel-loader": "^10.0.0"}}