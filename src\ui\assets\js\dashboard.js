// Dashboard JavaScript
class Dashboard {
    constructor() {
        this.statusUpdateInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPosts();
        this.startStatusUpdates();
    }

    bindEvents() {
        // Monitor controls
        document.getElementById('start-monitor').addEventListener('click', () => this.startMonitor());
        document.getElementById('stop-monitor').addEventListener('click', () => this.stopMonitor());
        
        // Test detection
        document.getElementById('test-detection').addEventListener('click', () => this.testDetection());
        
        // Refresh posts
        document.getElementById('refresh-posts').addEventListener('click', () => this.loadPosts());
        
        // Enter key for test detection
        document.getElementById('test-text').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrl<PERSON><PERSON>) {
                this.testDetection();
            }
        });
    }

    async startMonitor() {
        try {
            this.showLoading('start-monitor');
            const response = await fetch('/api/monitor/start', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showToast('Auto monitor started successfully!', 'success');
                this.updateMonitorButtons(true);
            } else {
                this.showToast(result.error || 'Failed to start monitor', 'error');
            }
        } catch (error) {
            this.showToast('Error starting monitor: ' + error.message, 'error');
        } finally {
            this.hideLoading('start-monitor');
        }
    }

    async stopMonitor() {
        try {
            this.showLoading('stop-monitor');
            const response = await fetch('/api/monitor/stop', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showToast('Auto monitor stopped successfully!', 'success');
                this.updateMonitorButtons(false);
            } else {
                this.showToast(result.error || 'Failed to stop monitor', 'error');
            }
        } catch (error) {
            this.showToast('Error stopping monitor: ' + error.message, 'error');
        } finally {
            this.hideLoading('stop-monitor');
        }
    }

    async testDetection() {
        const textArea = document.getElementById('test-text');
        const text = textArea.value.trim();
        
        if (!text) {
            this.showToast('Please enter text to test', 'warning');
            return;
        }

        try {
            this.showLoading('test-detection');
            const response = await fetch('/api/test-detection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });
            
            const result = await response.json();
            this.displayTestResult(result);
        } catch (error) {
            this.showToast('Error testing detection: ' + error.message, 'error');
        } finally {
            this.hideLoading('test-detection');
        }
    }

    displayTestResult(result) {
        const container = document.getElementById('test-result');
        const isSpam = result.is_spam;
        const confidence = (result.confidence * 100).toFixed(1);
        
        container.innerHTML = `
            <div class="test-result ${isSpam ? 'spam' : 'normal'} fade-in">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-${isSpam ? 'danger' : 'success'} prediction-badge">
                        ${result.label.toUpperCase()}
                    </span>
                    <small class="text-muted">Confidence: ${confidence}%</small>
                </div>
                <div class="confidence-bar">
                    <div class="confidence-fill ${this.getConfidenceClass(result.confidence)}" 
                         style="width: ${confidence}%"></div>
                </div>
                ${result.error ? `<div class="text-danger mt-2"><small>Error: ${result.error}</small></div>` : ''}
            </div>
        `;
    }

    async loadPosts() {
        try {
            this.showLoading('refresh-posts');
            const response = await fetch('/api/posts');
            const posts = await response.json();
            
            if (response.ok) {
                await this.displayPosts(posts);
            } else {
                this.showToast(posts.error || 'Failed to load posts', 'error');
                document.getElementById('posts-container').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${posts.error || 'Failed to load posts'}
                    </div>
                `;
            }
        } catch (error) {
            this.showToast('Error loading posts: ' + error.message, 'error');
        } finally {
            this.hideLoading('refresh-posts');
        }
    }

    async displayPosts(posts) {
        const container = document.getElementById('posts-container');

        if (posts.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-inbox fa-3x mb-3"></i>
                    <p>No posts found</p>
                </div>
            `;
            return;
        }

        let html = '';
        for (const post of posts) {
            html += this.renderPost(post);
        }

        container.innerHTML = html;

        // Bind click events to post headers
        this.bindPostClickEvents();
    }

    async loadCommentsForPost(postId, retries = 2) {
        try {
            console.log(`📡 Fetching comments for post ${postId}...`);

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

            const response = await fetch(`/api/posts/${postId}/comments`, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const comments = await response.json();
                console.log(`✅ Received ${comments.length} comments`);
                return comments;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error(`❌ Error loading comments (attempt ${3 - retries}):`, error.message);

            if (retries > 0 && !error.name === 'AbortError') {
                console.log(`🔄 Retrying... (${retries} attempts left)`);
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                return this.loadCommentsForPost(postId, retries - 1);
            }

            throw error;
        }
    }

    renderPost(post) {
        const postDate = new Date(post.created_time).toLocaleString();
        const postIdShort = post.id.split('_')[1];

        return `
            <div class="post-item fade-in" data-post-id="${post.id}">
                <div class="post-header" onclick="dashboard.toggleComments('${post.id}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-file-text me-2 text-primary"></i>
                                <h6 class="mb-0">Post ${postIdShort}</h6>
                                <i class="fas fa-chevron-down ms-2 expand-icon" id="icon-${post.id}"></i>
                            </div>
                            <p class="text-muted mb-2">${post.message ? post.message.substring(0, 150) + (post.message.length > 150 ? '...' : '') : 'No message'}</p>
                            <div class="d-flex align-items-center">
                                <small class="text-muted me-3">
                                    <i class="fas fa-calendar me-1"></i>
                                    ${postDate}
                                </small>
                                <span class="badge bg-info me-2" id="comment-count-${post.id}">
                                    <i class="fas fa-comments me-1"></i>
                                    Loading...
                                </span>
                                <span class="badge bg-warning" id="spam-count-${post.id}" style="display: none;">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Spam detected
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="comments-section" id="comments-${post.id}">
                    <div class="comments-header">
                        <i class="fas fa-comments me-2"></i>
                        Comments
                        <span class="float-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="dashboard.refreshComments('${post.id}')">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </span>
                    </div>
                    <div class="comments-container" id="comments-container-${post.id}">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 mb-0">Loading comments...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindPostClickEvents() {
        // Event listeners sudah di-handle oleh onclick di HTML
        // Method ini untuk future enhancements
    }

    async toggleComments(postId) {
        const commentsSection = document.getElementById(`comments-${postId}`);
        const expandIcon = document.getElementById(`icon-${postId}`);
        const postItem = document.querySelector(`[data-post-id="${postId}"]`);

        if (commentsSection.classList.contains('show')) {
            // Hide comments
            commentsSection.classList.remove('show');
            expandIcon.classList.remove('expanded');
            postItem.classList.remove('expanded');
        } else {
            // Show comments and load if not loaded yet
            commentsSection.classList.add('show');
            expandIcon.classList.add('expanded');
            postItem.classList.add('expanded');

            // Load comments if not loaded yet
            const container = document.getElementById(`comments-container-${postId}`);
            if (container.innerHTML.includes('Loading comments...')) {
                await this.loadAndDisplayComments(postId);
            }
        }
    }

    async loadAndDisplayComments(postId) {
        const container = document.getElementById(`comments-container-${postId}`);
        const commentCountBadge = document.getElementById(`comment-count-${postId}`);
        const spamCountBadge = document.getElementById(`spam-count-${postId}`);

        try {
            console.log(`🔄 Loading comments for post ${postId}...`);
            const startTime = Date.now();

            // Show enhanced loading state
            container.innerHTML = `
                <div class="text-center p-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-1">Loading comments...</p>
                    <small class="text-muted">Analyzing with AI...</small>
                </div>
            `;

            const comments = await this.loadCommentsForPost(postId);
            const loadTime = Date.now() - startTime;
            console.log(`⚡ Comments loaded in ${loadTime}ms`);

            if (comments.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted p-3">
                        <i class="fas fa-comment-slash fa-2x mb-2"></i>
                        <p class="mb-0">No comments found</p>
                    </div>
                `;
                commentCountBadge.innerHTML = '<i class="fas fa-comments me-1"></i>0 Comments';
                return;
            }

            // Progressive rendering - show comments as they're processed
            container.innerHTML = '<div class="comments-list"></div>';
            const commentsList = container.querySelector('.comments-list');

            // Render comments progressively
            comments.forEach((comment, index) => {
                setTimeout(() => {
                    const commentElement = document.createElement('div');
                    commentElement.innerHTML = this.renderComment(comment);
                    commentElement.style.opacity = '0';
                    commentElement.style.transform = 'translateY(10px)';

                    commentsList.appendChild(commentElement.firstElementChild);

                    // Animate in
                    requestAnimationFrame(() => {
                        commentElement.firstElementChild.style.transition = 'all 0.3s ease';
                        commentElement.firstElementChild.style.opacity = '1';
                        commentElement.firstElementChild.style.transform = 'translateY(0)';
                    });
                }, index * 50); // Stagger animation
            });

            // Update badges
            const spamCount = comments.filter(c => c.prediction?.is_spam).length;
            commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${comments.length} Comments`;

            if (spamCount > 0) {
                spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamCount} Spam`;
                spamCountBadge.style.display = 'inline-block';
            } else {
                spamCountBadge.style.display = 'none';
            }

        } catch (error) {
            console.error(`❌ Error loading comments:`, error);
            container.innerHTML = `
                <div class="alert alert-danger m-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading comments: ${error.message}
                    <br><small class="text-muted">Check console for details</small>
                </div>
            `;
            commentCountBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Error';
        }
    }

    async refreshComments(postId) {
        const container = document.getElementById(`comments-container-${postId}`);
        container.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-1">Refreshing comments...</p>
                <small class="text-muted">Clearing cache and reloading...</small>
            </div>
        `;

        // Force refresh by adding query parameter
        try {
            const response = await fetch(`/api/posts/${postId}/comments?refresh=true`);
            if (response.ok) {
                const comments = await response.json();
                this.displayCommentsInContainer(postId, comments);
                this.showToast('Comments refreshed successfully!', 'success');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Error refreshing comments:', error);
            container.innerHTML = `
                <div class="alert alert-warning m-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to refresh comments. Please try again.
                </div>
            `;
            this.showToast('Failed to refresh comments', 'error');
        }
    }

    displayCommentsInContainer(postId, comments) {
        const container = document.getElementById(`comments-container-${postId}`);
        const commentCountBadge = document.getElementById(`comment-count-${postId}`);
        const spamCountBadge = document.getElementById(`spam-count-${postId}`);

        if (comments.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted p-3">
                    <i class="fas fa-comment-slash fa-2x mb-2"></i>
                    <p class="mb-0">No comments found</p>
                </div>
            `;
            commentCountBadge.innerHTML = '<i class="fas fa-comments me-1"></i>0 Comments';
            return;
        }

        // Render comments
        container.innerHTML = comments.map(comment => this.renderComment(comment)).join('');

        // Update badges
        const spamCount = comments.filter(c => c.prediction?.is_spam).length;
        commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${comments.length} Comments`;

        if (spamCount > 0) {
            spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamCount} Spam`;
            spamCountBadge.style.display = 'inline-block';
        } else {
            spamCountBadge.style.display = 'none';
        }
    }

    renderComment(comment) {
        const prediction = comment.prediction;
        const isSpam = prediction?.is_spam || false;
        const confidence = prediction?.confidence || 0;
        const commentDate = new Date(comment.created_time).toLocaleString();

        return `
            <div class="comment-item ${isSpam ? 'spam' : 'normal'}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2">${comment.from?.name || 'Unknown'}</strong>
                            <span class="badge bg-${isSpam ? 'danger' : 'success'} prediction-badge">
                                ${prediction?.label?.toUpperCase() || 'UNKNOWN'}
                            </span>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${commentDate}
                        </small>
                    </div>
                    ${isSpam ? `
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-danger dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-exclamation-triangle"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item text-danger" href="#" onclick="dashboard.deleteComment('${comment.id}')">
                                <i class="fas fa-trash me-2"></i>Delete Comment
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="dashboard.markAsNotSpam('${comment.id}')">
                                <i class="fas fa-check me-2"></i>Mark as Not Spam
                            </a></li>
                        </ul>
                    </div>
                    ` : ''}
                </div>
                <p class="mb-2">${comment.message}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <div class="confidence-bar flex-grow-1 me-3">
                        <div class="confidence-fill ${this.getConfidenceClass(confidence)}"
                             style="width: ${(confidence * 100).toFixed(1)}%"></div>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-brain me-1"></i>
                        ${(confidence * 100).toFixed(1)}%
                    </small>
                </div>
            </div>
        `;
    }

    async deleteComment(commentId) {
        if (!confirm('Are you sure you want to delete this comment?')) {
            return;
        }

        try {
            const response = await fetch(`/api/comments/${commentId}`, { method: 'DELETE' });
            const result = await response.json();

            if (result.success) {
                this.showToast('Comment deleted successfully!', 'success');

                // Remove comment from UI instead of full refresh
                const commentElement = document.querySelector(`[onclick*="${commentId}"]`)?.closest('.comment-item');
                if (commentElement) {
                    commentElement.style.animation = 'fadeOut 0.3s ease-out';
                    setTimeout(() => {
                        commentElement.remove();
                        this.updateCommentCounts();
                    }, 300);
                }
            } else {
                this.showToast(result.error || 'Failed to delete comment', 'error');
            }
        } catch (error) {
            this.showToast('Error deleting comment: ' + error.message, 'error');
        }
    }

    async markAsNotSpam(commentId) {
        // This is a placeholder for future implementation
        // Could be used to retrain the model or add to whitelist
        this.showToast('Feature coming soon: Mark as not spam', 'info');
    }

    updateCommentCounts() {
        // Update comment counts after deletion
        document.querySelectorAll('[data-post-id]').forEach(postElement => {
            const postId = postElement.dataset.postId;
            const commentsContainer = document.getElementById(`comments-container-${postId}`);
            const commentCountBadge = document.getElementById(`comment-count-${postId}`);
            const spamCountBadge = document.getElementById(`spam-count-${postId}`);

            if (commentsContainer) {
                const totalComments = commentsContainer.querySelectorAll('.comment-item').length;
                const spamComments = commentsContainer.querySelectorAll('.comment-item.spam').length;

                commentCountBadge.innerHTML = `<i class="fas fa-comments me-1"></i>${totalComments} Comments`;

                if (spamComments > 0) {
                    spamCountBadge.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>${spamComments} Spam`;
                    spamCountBadge.style.display = 'inline-block';
                } else {
                    spamCountBadge.style.display = 'none';
                }
            }
        });
    }

    startStatusUpdates() {
        this.statusUpdateInterval = setInterval(() => {
            this.updateStatus();
        }, 5000); // Update every 5 seconds
    }

    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            // Update monitor status
            const monitorStatus = document.getElementById('monitor-status');
            const isRunning = status.monitor.isRunning;
            
            monitorStatus.textContent = isRunning ? 'Running' : 'Stopped';
            monitorStatus.className = `badge bg-${isRunning ? 'success' : 'secondary'}`;
            
            // Update statistics
            document.getElementById('comments-processed').textContent = status.monitor.commentsProcessed;
            document.getElementById('spam-removed').textContent = status.monitor.spamRemoved;
            
            // Update buttons
            this.updateMonitorButtons(isRunning);
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }

    updateMonitorButtons(isRunning) {
        const startBtn = document.getElementById('start-monitor');
        const stopBtn = document.getElementById('stop-monitor');
        
        startBtn.disabled = isRunning;
        stopBtn.disabled = !isRunning;
    }

    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'high';
        if (confidence >= 0.5) return 'medium';
        return 'low';
    }

    showLoading(buttonId) {
        const button = document.getElementById(buttonId);
        const originalText = button.innerHTML;
        button.dataset.originalText = originalText;
        button.innerHTML = '<span class="loading-spinner me-2"></span>Loading...';
        button.disabled = true;
    }

    hideLoading(buttonId) {
        const button = document.getElementById(buttonId);
        button.innerHTML = button.dataset.originalText;
        button.disabled = false;
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = toast.querySelector('.toast-body');
        const toastHeader = toast.querySelector('.toast-header');
        
        // Set icon based on type
        const icons = {
            success: 'fas fa-check-circle text-success',
            error: 'fas fa-exclamation-circle text-danger',
            warning: 'fas fa-exclamation-triangle text-warning',
            info: 'fas fa-info-circle text-info'
        };
        
        toastHeader.querySelector('i').className = icons[type] || icons.info;
        toastBody.textContent = message;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});
