# Facebook Graph API Configuration
PAGE_ID=your_facebook_page_id_here
PAGE_ACCESS_TOKEN=your_facebook_page_access_token_here
FB_APP_SECRET=your_app_secret_here

# Webhook Configuration
WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here
WEBHOOK_PORT=3000

# Auto Monitor Configuration
MONITOR_POLL_INTERVAL=30000

# Python Configuration (optional)
PYTHON_PATH=python
# PYTHON_PATH=python3  # Uncomment if using python3 command

# Spam Detector Configuration
SPAM_DETECTOR_MODE=child_process
# SPAM_DETECTOR_MODE=http  # Uncomment to use HTTP API mode
SPAM_DETECTOR_API_URL=http://localhost:5000
SPAM_DETECTOR_TIMEOUT=30000

# Model Configuration
MODEL_PATH=./Model
CONFIDENCE_THRESHOLD=0.8
